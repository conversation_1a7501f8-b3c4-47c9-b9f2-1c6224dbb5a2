'use client';

import { useEffect } from 'react';

export default function WebVitalsMonitor() {
  useEffect(() => {
    // Only run in production or when explicitly enabled
    if (process.env.NODE_ENV !== 'production' && !process.env.NEXT_PUBLIC_MONITOR_VITALS) {
      return;
    }

    // Web Vitals monitoring
    const vitalsData = {
      lcp: null,
      fid: null,
      cls: null,
      fcp: null,
      ttfb: null
    };

    // Function to send vitals to analytics
    const sendToAnalytics = (metric) => {
      // Send to Google Analytics 4
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', metric.name, {
          event_category: 'Web Vitals',
          event_label: metric.id,
          value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
          non_interaction: true,
        });
      }

      // Send to Vercel Analytics
      if (typeof window !== 'undefined' && window.va) {
        window.va('track', 'Web Vitals', {
          metric: metric.name,
          value: metric.value,
          id: metric.id,
          url: window.location.pathname
        });
      }

      // Log to console in development
      if (process.env.NODE_ENV === 'development') {
        console.log(`${metric.name}:`, metric.value, metric);
      }

      // Store for potential API sending
      vitalsData[metric.name.toLowerCase()] = metric.value;
    };

    // Largest Contentful Paint (LCP)
    const observeLCP = () => {
      if ('PerformanceObserver' in window) {
        try {
          const observer = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const lastEntry = entries[entries.length - 1];
            
            sendToAnalytics({
              name: 'LCP',
              value: lastEntry.startTime,
              id: generateUniqueId(),
              entries: entries
            });
          });
          
          observer.observe({ entryTypes: ['largest-contentful-paint'] });
        } catch (e) {
          console.warn('LCP observation failed:', e);
        }
      }
    };

    // First Input Delay (FID)
    const observeFID = () => {
      if ('PerformanceObserver' in window) {
        try {
          const observer = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            entries.forEach((entry) => {
              sendToAnalytics({
                name: 'FID',
                value: entry.processingStart - entry.startTime,
                id: generateUniqueId(),
                entries: [entry]
              });
            });
          });
          
          observer.observe({ entryTypes: ['first-input'] });
        } catch (e) {
          console.warn('FID observation failed:', e);
        }
      }
    };

    // Cumulative Layout Shift (CLS)
    const observeCLS = () => {
      if ('PerformanceObserver' in window) {
        try {
          let clsValue = 0;
          let sessionValue = 0;
          let sessionEntries = [];

          const observer = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            
            entries.forEach((entry) => {
              // Only count layout shifts without recent input
              if (!entry.hadRecentInput) {
                const firstSessionEntry = sessionEntries[0];
                const lastSessionEntry = sessionEntries[sessionEntries.length - 1];

                // If the entry occurred less than 1 second after the previous entry
                // and less than 5 seconds after the first entry in the session,
                // include the entry in the current session. Otherwise, start a new session.
                if (sessionValue &&
                    entry.startTime - lastSessionEntry.startTime < 1000 &&
                    entry.startTime - firstSessionEntry.startTime < 5000) {
                  sessionValue += entry.value;
                  sessionEntries.push(entry);
                } else {
                  sessionValue = entry.value;
                  sessionEntries = [entry];
                }

                // If the current session value is larger than the current CLS value,
                // update CLS and the entries contributing to it.
                if (sessionValue > clsValue) {
                  clsValue = sessionValue;
                  
                  sendToAnalytics({
                    name: 'CLS',
                    value: clsValue,
                    id: generateUniqueId(),
                    entries: sessionEntries
                  });
                }
              }
            });
          });
          
          observer.observe({ entryTypes: ['layout-shift'] });
        } catch (e) {
          console.warn('CLS observation failed:', e);
        }
      }
    };

    // First Contentful Paint (FCP)
    const observeFCP = () => {
      if ('PerformanceObserver' in window) {
        try {
          const observer = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            entries.forEach((entry) => {
              if (entry.name === 'first-contentful-paint') {
                sendToAnalytics({
                  name: 'FCP',
                  value: entry.startTime,
                  id: generateUniqueId(),
                  entries: [entry]
                });
              }
            });
          });
          
          observer.observe({ entryTypes: ['paint'] });
        } catch (e) {
          console.warn('FCP observation failed:', e);
        }
      }
    };

    // Time to First Byte (TTFB)
    const observeTTFB = () => {
      if ('PerformanceObserver' in window) {
        try {
          const observer = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            entries.forEach((entry) => {
              if (entry.entryType === 'navigation') {
                sendToAnalytics({
                  name: 'TTFB',
                  value: entry.responseStart - entry.requestStart,
                  id: generateUniqueId(),
                  entries: [entry]
                });
              }
            });
          });
          
          observer.observe({ entryTypes: ['navigation'] });
        } catch (e) {
          console.warn('TTFB observation failed:', e);
        }
      }
    };

    // Generate unique ID for each metric
    const generateUniqueId = () => {
      return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    };

    // Start observing all metrics
    observeLCP();
    observeFID();
    observeCLS();
    observeFCP();
    observeTTFB();

    // Additional performance monitoring
    const monitorResourceTiming = () => {
      if ('PerformanceObserver' in window) {
        try {
          const observer = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            
            entries.forEach((entry) => {
              // Monitor slow resources (>1s)
              if (entry.duration > 1000) {
                console.warn('Slow resource detected:', {
                  name: entry.name,
                  duration: entry.duration,
                  size: entry.transferSize
                });

                // Send to analytics
                if (typeof window !== 'undefined' && window.gtag) {
                  window.gtag('event', 'slow_resource', {
                    event_category: 'Performance',
                    event_label: entry.name,
                    value: Math.round(entry.duration)
                  });
                }
              }
            });
          });
          
          observer.observe({ entryTypes: ['resource'] });
        } catch (e) {
          console.warn('Resource timing observation failed:', e);
        }
      }
    };

    // Monitor long tasks (>50ms)
    const monitorLongTasks = () => {
      if ('PerformanceObserver' in window) {
        try {
          const observer = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            
            entries.forEach((entry) => {
              console.warn('Long task detected:', {
                duration: entry.duration,
                startTime: entry.startTime
              });

              // Send to analytics
              if (typeof window !== 'undefined' && window.gtag) {
                window.gtag('event', 'long_task', {
                  event_category: 'Performance',
                  value: Math.round(entry.duration)
                });
              }
            });
          });
          
          observer.observe({ entryTypes: ['longtask'] });
        } catch (e) {
          console.warn('Long task observation failed:', e);
        }
      }
    };

    // Start additional monitoring
    monitorResourceTiming();
    monitorLongTasks();

    // Send aggregated vitals data to API endpoint (optional)
    const sendVitalsToAPI = () => {
      if (Object.values(vitalsData).some(value => value !== null)) {
        fetch('/api/vitals', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            ...vitalsData,
            url: window.location.pathname,
            userAgent: navigator.userAgent,
            timestamp: Date.now()
          })
        }).catch(err => {
          console.warn('Failed to send vitals to API:', err);
        });
      }
    };

    // Send vitals data when page is about to unload
    window.addEventListener('beforeunload', sendVitalsToAPI);

    // Cleanup
    return () => {
      window.removeEventListener('beforeunload', sendVitalsToAPI);
    };

  }, []);

  return null;
}
