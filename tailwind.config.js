/** @type {import('tailwindcss').Config} */
export default {
  darkMode: ['class'],
  content: [
    './src/**/*.{js,jsx,ts,tsx}',
    './src/app/**/*.{js,jsx,ts,tsx}',
    './src/components/**/*.{js,jsx,ts,tsx}',
    './src/pages/**/*.{js,jsx,ts,tsx}',
  ],
  corePlugins: {
    preflight: true,
  },
  theme: {
    extend: {
      colors: {
        // BAKASANA Premium Wellness Kolorystyka
        primary: '#333333',        // Ciemnoszary tekst - NIE czarna
        secondary: '#FFFFFF',      // Czysta biel
        accent: '#7C9885',         // Szałwiowa zieleń
        background: '#FAFAF8',     // Bardzo delikatny beż
        muted: '#F5F5F5',          // Bard<PERSON> jasny szary
        'accent-warm': '#D4B5A0',  // Ciepły piaskowy
        'text-light': '#666666',   // Jasny tekst

        // Dodatkowe odcienie dla większej elast<PERSON>
        'accent-light': '#A8C4B0',  // Jaśniejszy odcień szałwiowego
        'accent-dark': '#5A7A63',   // Ciemniejszy odcień szałwiowego
        'primary-light': '#6B6B6B', // Jaśniejszy odcień szarego
        'background-alt': '#F8F8F6', // Alternatywne tło
      },
      fontFamily: {
        sans: ['var(--font-sans)', 'Source Sans Pro', 'Open Sans', 'system-ui', 'sans-serif'],
        serif: ['var(--font-serif)', 'Playfair Display', 'Cormorant Garamond', 'Georgia', 'serif'],
        display: ['var(--font-serif)', 'Playfair Display', 'Cormorant Garamond', 'Georgia', 'serif'],
      },
      fontSize: {
        sm: ['0.875rem', { lineHeight: '1.6', letterSpacing: '0.025em' }],
        base: ['1rem', { lineHeight: '1.7' }],
        lg: ['1.125rem', { lineHeight: '1.8' }],
        xl: ['1.25rem', { lineHeight: '1.8', letterSpacing: '-0.005em' }],
        '2xl': ['1.5rem', { lineHeight: '1.7', letterSpacing: '-0.01em' }],
        '3xl': ['1.875rem', { lineHeight: '1.6', letterSpacing: '-0.015em' }],
        '4xl': ['2.25rem', { lineHeight: '1.5', letterSpacing: '-0.02em' }],
        '5xl': ['3rem', { lineHeight: '1.4', letterSpacing: '-0.025em' }],
        '6xl': ['3.75rem', { lineHeight: '1.3', letterSpacing: '-0.03em' }],
      },
      spacing: {
        xs: '0.5rem',
        sm: '1rem',
        md: '1.5rem',
        lg: '2rem',
        xl: '3rem',
        '2xl': '4rem',
        '3xl': '6rem',
        '4xl': '8rem',
        '5xl': '10rem',   // Ultra duża przestrzeń dla minimalizmu
        '6xl': '12rem',   // Maksymalna przestrzeń dla sekcji
      },
      height: {
        hero: '100vh',
        'hero-sm': '85vh',
      },
      maxWidth: {
        content: '80rem',
        '6xl': '72rem',
      },
      animation: {
        'fade-in': 'fadeIn 0.6s ease-out forwards',
        'gentle-hover': 'gentleHover 0.3s ease-out forwards',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        gentleHover: {
          'to': { transform: 'translateY(-1px)' },
        },
      },
      transitionTimingFunction: {
        'ease-gentle': 'cubic-bezier(0.23, 1, 0.32, 1)',
      },
      transitionDuration: {
        gentle: '300ms',
      },
      boxShadow: {
        soft: '0 1px 3px rgba(0, 0, 0, 0.02)',
        medium: '0 2px 8px rgba(0, 0, 0, 0.04)',
        subtle: '0 1px 2px rgba(0, 0, 0, 0.01)',
      },
      borderRadius: {
        xl: '0.75rem', 
        lg: '0.5rem',
        md: '0.375rem',
        sm: '0.25rem',
      },
      backgroundImage: {
        'gradient-soft': 'linear-gradient(135deg, rgba(250, 250, 248, 0.95), rgba(255, 255, 255, 0.98))',
      },
      backdropBlur: {
        sm: '4px',
        md: '8px',
      },
    },
  },
  plugins: [],
};