import React from 'react';
import { Suspense } from 'react';
import ContactForm from './ContactForm'; // Import komponentu klienckiego
import { Mail } from 'lucide-react'; // Ikona dla tytułu

// Metadane są w osobnym pliku metadata.js

export default function KontaktPage() {
  return (
    <main className="relative section-bg min-h-screen">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
        {/* Nagłówek strony */}
        <header className="text-center mb-16">
          <div className="inline-flex items-center justify-center p-3 bg-temple/10 rounded-full mb-6">
             <Mail className="w-8 h-8 text-temple/70" />
          </div>
          <h1 className="text-4xl md:text-5xl font-serif text-temple tracking-tight mb-6 font-light">
            Kontakt
          </h1>
          <div className="decorative-line"></div>
          <p className="text-lg text-wood-light/80 max-w-3xl mx-auto font-light">
            Masz pytania lub chcesz zarezerwować miejsce? Skontaktuj się z nami!
          </p>
        </header>

        {/* Renderowanie formularza i sekcji IG z komponentu klienckiego */}
        <Suspense fallback={<div className="flex items-center justify-center h-[40vh] text-wood-light">Ładowanie formularza...</div>}>
          <ContactForm />
        </Suspense>
      </div>
    </main>
  );
}