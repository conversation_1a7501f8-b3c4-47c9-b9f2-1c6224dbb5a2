import React from 'react';
import Image from 'next/image';
import { generateMetadata as generateSEOMetadata, generateStructuredData } from '../metadata';

export const metadata = generateSEOMetadata({
  title: '<PERSON> - Instruktorka Jogi i Fizjoterapeutka',
  description: 'Poznaj Jul<PERSON> - magistra fizjoterapii z 8-letnim doświadczeniem i certyfikowaną instruktorkę jogi RYT 500. Organizatorka retreatów jogowych na Bali od 2020 roku.',
  keywords: [
    '<PERSON>',
    'instruktorka jogi RYT 500',
    'fizjoterapeutka magister',
    'joga terapeutyczna',
    'organizator retreatów Bali',
    'Yoga Alliance',
    'doświadczenie fizjoterapia',
    'holistyczne podejście joga'
  ],
});

export default function OMniePage() {
  const structuredData = generateStructuredData({
    type: 'Person',
    name: '<PERSON>',
    description: 'Magister fiz<PERSON><PERSON>pi<PERSON> i certyfikowana instruktorka jogi RYT 500. Organizatorka retreatów jogowych na Bali.',
  });

  const qualifications = [
    'Magister fizjoterapii z 8-letnim doświadczeniem klinicznym',
    'Certyfikowana instruktorka jogi (RYT 500) - Yoga Alliance',
    'Specjalizacja w jodze terapeutycznej i rehabilitacyjnej',
    'Ukończone kursy: Vinyasa, Hatha, Yin Yoga, Pranayama',
    'Organizator retreatów jogowych na Bali od 2020 roku',
    'Autorka programów łączących fizjoterapię z praktyką jogi',
    'Współpraca z ośrodkami rehabilitacyjnymi i studiami jogi',
    'Certyfikat w zakresie anatomii i biomechaniki ruchu'
  ];

  const achievements = [
    {
      title: 'Ponad 2000 godzin nauczania jogi',
      description: 'Doświadczenie w pracy z różnymi grupami wiekowymi i poziomami zaawansowania',
      category: 'Doświadczenie'
    },
    {
      title: 'Kilka udanych retreatów na Bali',
      description: 'Zorganizowane transformacyjne wyjazdy dla uczestników z całej Polski',
      category: 'Retreaty'
    },
    {
      title: 'Autorska metodyka łączenia fizjoterapii z jogą',
      description: 'Innowacyjne podejście do terapii bólu pleców i problemów postawy',
      category: 'Specjalizacja'
    },
    {
      title: 'Współpraca z ekspertami',
      description: 'Stała współpraca z lekarzami ortopedami i fizjoterapeutami',
      category: 'Partnerstwa'
    }
  ];

  const philosophy = [
    {
      title: 'Holistyczne podejście',
      description: 'Łączę wiedzę medyczną z duchową praktyką jogi, tworząc kompleksowe programy zdrowotne.',
      category: 'Metodyka'
    },
    {
      title: 'Indywidualizacja',
      description: 'Każdy uczestnik otrzymuje personalne wskazówki dostosowane do jego potrzeb i możliwości.',
      category: 'Podejście'
    },
    {
      title: 'Bezpieczeństwo przede wszystkim',
      description: 'Moje doświadczenie fizjoterapeutyczne gwarantuje bezpieczną praktykę dla wszystkich.',
      category: 'Priorytet'
    },
    {
      title: 'Transformacja przez podróż',
      description: 'Wierzę, że połączenie jogi z magią Bali tworzy przestrzeń dla głębokiej przemiany.',
      category: 'Wizja'
    }
  ];

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      
      <main className="relative min-h-screen bg-gradient-to-b from-rice/90 via-mist/50 to-ocean-light/10">
        {/* Hero Section */}
        <section className="section section-padding">
          <div className="max-width-content container-padding">
            <div className="section-title">
              <h1 className="text-4xl md:text-5xl font-serif text-temple mb-6 font-light">
                Julia Jakubowicz
              </h1>
              <p className="text-lg text-wood-light leading-relaxed font-light">
                Poznaj moją drogę od fizjoterapii do instruktorki jogi i organizatorki transformacyjnych retreatów na Bali
              </p>
              <div className="decorative-line" />
            </div>
          </div>
        </section>

        {/* Main Profile Section */}
        <section className="section section-padding">
          <div className="max-width-content container-padding">
            <div className="card p-12">
              <div className="md:flex items-start gap-12">
                {/* Profile Content */}
                <div className="md:w-3/5 mb-8 md:mb-0">
                  <h2 className="text-3xl font-serif text-temple mb-4 font-light">
                    Pasja do holistycznego zdrowia
                  </h2>

                  <p className="text-wood-light leading-relaxed mb-8 font-light">
                    Jestem magistrem fizjoterapii z 8-letnim doświadczeniem klinicznym oraz certyfikowaną
                    instruktorką jogi (RYT 500). Moją pasją jest łączenie wiedzy medycznej z duchową
                    praktyką jogi, tworząc holistyczne podejście do zdrowia i dobrostanu.
                  </p>

                  <div className="space-y-6 text-wood-light leading-relaxed font-light">
                    <p>
                      Organizuję retreaty jogowe na Bali, gdzie dzielę się nie tylko technikami
                      jogi, ale także magią tej wyjątkowej wyspy. Moje programy łączą tradycyjną praktykę
                      z nowoczesną wiedzą o anatomii i biomechanice.
                    </p>

                    <p>
                      W Polsce prowadzę <a href="https://flywithbakasana.pl/" target="_blank" rel="noopener noreferrer" className="text-temple hover:text-golden transition-colors">Studio Bakasana w Rzeszowie</a>,
                      gdzie regularnie uczę różnych stylów jogi: Hatha, Vinyasa, Ashtanga Flow, jogi dla kobiet w ciąży oraz seniorów.
                    </p>

                    <p>
                      Specjalizuję się w jodze terapeutycznej, pomagając osobom z problemami kręgosłupa,
                      bólami pleców i zaburzeniami postawy. Wierzę, że każdy może praktykować jogę -
                      niezależnie od wieku, kondycji czy doświadczenia.
                    </p>
                  </div>
                </div>

                {/* Profile Image */}
                <div className="md:w-2/5">
                  <div className="h-80 md:h-96 relative rounded-lg overflow-hidden">
                    <Image
                      src="/images/profile/omnie-opt.webp"
                      alt="Julia Jakubowicz - Instruktorka jogi i fizjoterapeutka"
                      fill
                      priority
                      className="object-cover"
                      sizes="(max-width: 768px) 100vw, 40vw"
                    />
                    <div className="absolute inset-0 bg-temple/5" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Qualifications */}
        <section className="section section-padding">
          <div className="max-width-content container-padding">
            <div className="section-title">
              <h3 className="text-3xl font-serif text-temple mb-6 font-light">
                Kwalifikacje i Doświadczenie
              </h3>
              <p className="text-wood-light mb-8 font-light">Profesjonalne wykształcenie i certyfikaty</p>
              <div className="decorative-line" />
            </div>

            <div className="card p-8">
              <div className="space-y-3">
                {qualifications.map((qualification, index) => (
                  <div key={index} className="flex items-start gap-3 py-2 border-b border-temple/10">
                    <div className="w-1 h-1 bg-temple/40 mt-2 flex-shrink-0" />
                    <span className="text-wood-light leading-relaxed font-light">
                      {qualification}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Achievements */}
        <section className="section section-padding">
          <div className="max-width-content container-padding">
            <div className="section-title">
              <h3 className="text-3xl font-serif text-temple mb-6 font-light">
                Profesjonalne Osiągnięcia
              </h3>
              <p className="text-wood-light/85 mb-8 font-light">Moje najważniejsze sukcesy zawodowe</p>
              <div className="decorative-line" />
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              {achievements.map((achievement, index) => (
                <div key={index} className="card p-6">
                  <h4 className="text-lg font-serif text-temple mb-3 font-light">
                    {achievement.title}
                  </h4>
                  <p className="text-wood-light/80 leading-relaxed font-light">
                    {achievement.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Philosophy */}
        <section className="section section-padding">
          <div className="max-width-content container-padding">
            <div className="section-title">
              <h3 className="text-3xl font-serif text-temple mb-6 font-light">
                Filozofia Pracy
              </h3>
              <p className="text-wood-light/85 mb-8 font-light">Wartości które kierują moją praktyką</p>
              <div className="decorative-line" />
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              {philosophy.map((item, index) => (
                <div key={index} className="card p-6">
                  <h4 className="text-lg font-serif text-temple mb-3 font-light">
                    {item.title}
                  </h4>
                  <p className="text-wood-light/80 leading-relaxed font-light">
                    {item.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <section className="section section-padding">
          <div className="max-width-content container-padding">
            <div className="glass-effect bg-gradient-to-r from-bamboo/10 to-lotus/20 rounded-3xl p-12 text-center">
              <h3 className="text-2xl font-serif text-temple mb-6 font-light">
                Gotowa na transformację?
              </h3>

              <p className="text-wood-light/85 leading-relaxed mb-8 font-light">
                Dołącz do mnie na Bali i odkryj, jak joga może zmienić Twoje życie.
                Każdy retreat to unikalna podróż łącząca praktykę z magią wyspy.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <a
                  href="/program"
                  className="btn-soft-golden"
                  aria-label="Zobacz programy retreatów jogowych na Bali"
                >
                  Zobacz Programy Retreatów
                </a>

                <span className="text-temple/50 text-sm">lub</span>

                <a
                  href="/kontakt"
                  className="btn-soft"
                  aria-label="Skontaktuj się z Julią"
                >
                  Skontaktuj się ze mną
                </a>
              </div>
            </div>
          </div>
        </section>
      </main>
    </>
  );
}