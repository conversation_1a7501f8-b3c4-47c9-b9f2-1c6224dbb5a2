// Critical CSS for above-the-fold content - only essential styles
export const criticalStyles = `
  /* CSS Variables - Critical colors only */
  :root {
    --color-primary: 44 44 44;         /* Ciemnoszary tekst (#2C2C2C) */
    --color-secondary: 255 255 255;    /* <PERSON><PERSON><PERSON> biel (#FFFFFF) */
    --color-accent: 124 152 133;       /* Ciepły szałwiowy (#7C9885) */
    --color-background: 250 250 248;   /* <PERSON><PERSON><PERSON> del<PERSON> o<PERSON>ń (#FAFAF8) */
    --color-muted: 245 245 245;        /* <PERSON><PERSON><PERSON> j<PERSON><PERSON> szar<PERSON> (#F5F5F5) */
    --font-sans: 'Source Sans Pro', 'system-ui', sans-serif;
    --font-serif: 'Playfair Display', 'Georgia', serif;
  }

  /* Critical base styles */
  * { box-sizing: border-box; }
  html { scroll-behavior: smooth; -webkit-font-smoothing: antialiased; }
  body {
    font-family: var(--font-sans);
    color: rgb(var(--color-primary));
    background: rgb(var(--color-background));
    margin: 0;
    line-height: 1.7;
    overflow-x: hidden;
  }

  /* Critical layout */
  .min-h-screen { min-height: 100vh; }
  .flex { display: flex; }
  .flex-col { flex-direction: column; }
  .items-center { align-items: center; }
  .justify-center { justify-content: center; }
  .text-center { text-align: center; }
  .relative { position: relative; }
  .absolute { position: absolute; }
  .inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
  .w-full { width: 100%; }
  .h-full { height: 100%; }
  .object-cover { object-fit: cover; }
  .overflow-hidden { overflow: hidden; }
  .z-10 { z-index: 10; }

  /* Critical typography */
  .font-serif { font-family: var(--font-playfair), serif; }
  .text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
  .text-5xl { font-size: 3rem; line-height: 1; }
  .text-6xl { font-size: 3.75rem; line-height: 1; }
  .text-lg { font-size: 1.125rem; line-height: 1.75rem; }
  .text-xl { font-size: 1.25rem; line-height: 1.75rem; }
  .leading-tight { line-height: 1.25; }
  .leading-relaxed { line-height: 1.625; }
  .tracking-tight { letter-spacing: -0.025em; }
  .drop-shadow-md { filter: drop-shadow(0 4px 3px rgb(0 0 0 / 0.07)) drop-shadow(0 2px 2px rgb(0 0 0 / 0.06)); }

  /* Critical colors */
  .text-primary { color: rgb(var(--color-primary)); }
  .text-accent { color: rgb(var(--color-accent)); }
  .bg-primary { background-color: rgb(var(--color-primary)); }
  .bg-secondary { background-color: rgb(var(--color-secondary)); }

  /* Critical spacing */
  .p-4 { padding: 1rem; }
  .p-6 { padding: 1.5rem; }
  .px-4 { padding-left: 1rem; padding-right: 1rem; }
  .px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
  .px-8 { padding-left: 2rem; padding-right: 2rem; }
  .py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
  .py-20 { padding-top: 5rem; padding-bottom: 5rem; }
  .mb-4 { margin-bottom: 1rem; }
  .mb-6 { margin-bottom: 1.5rem; }
  .mb-8 { margin-bottom: 2rem; }
  .mt-4 { margin-top: 1rem; }
  .mt-6 { margin-top: 1.5rem; }
  .mx-auto { margin-left: auto; margin-right: auto; }
  .max-w-2xl { max-width: 42rem; }
  .max-w-5xl { max-width: 64rem; }

  /* Critical responsive */
  @media (min-width: 640px) {
    .sm\\:text-5xl { font-size: 3rem; line-height: 1; }
    .sm\\:text-xl { font-size: 1.25rem; line-height: 1.75rem; }
    .sm\\:px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
  }

  @media (min-width: 768px) {
    .md\\:min-h-\\[90vh\\] { min-height: 90vh; }
  }

  @media (min-width: 1024px) {
    .lg\\:text-6xl { font-size: 3.75rem; line-height: 1; }
    .lg\\:px-8 { padding-left: 2rem; padding-right: 2rem; }
  }

  /* Critical hero styles */
  .glass-effect::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgb(var(--color-background) / 0.1), rgb(var(--color-muted) / 0.05));
  }

  /* Critical button base */
  .btn-primary {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 2rem;
    font-size: 0.875rem;
    font-weight: 400;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    color: rgb(var(--color-primary));
    background: transparent;
    border: 1px solid rgb(var(--color-primary));
    transition: all 0.3s ease;
    cursor: pointer;
    text-decoration: none;
  }

  .btn-primary:hover {
    background: rgb(var(--color-primary));
    color: rgb(var(--color-secondary));
  }
`;

export default function CriticalCSS() {
  return (
    <style
      dangerouslySetInnerHTML={{
        __html: criticalStyles,
      }}
    />
  );
}
