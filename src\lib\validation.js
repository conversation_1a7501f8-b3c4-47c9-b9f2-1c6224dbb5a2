import { z } from 'zod';

export const contactFormSchema = z.object({
  name: z.string()
    .min(2, 'Imię musi mieć co najmniej 2 znaki')
    .max(50, 'Imię nie może być dłuższe niż 50 znaków')
    .regex(/^[a-zA-ZąćęłńóśźżĄĆĘŁŃÓŚŹŻ\s-]+$/, 'Imię może zawierać tylko litery, spacje i myślniki'),
  
  email: z.string()
    .email('Nieprawidłowy adres email')
    .max(100, 'Email nie może być dłuższy niż 100 znaków'),
  
  message: z.string()
    .min(10, 'Wiadomość musi mieć co najmniej 10 znaków')
    .max(1000, 'Wiadomość nie może być dłuższa niż 1000 znaków'),
  
  phone: z.string()
    .regex(/^[0-9+\s-()]{9,15}$/, 'Nieprawidłowy numer telefonu')
    .optional(),
});

export const eventRegistrationSchema = z.object({
  name: z.string()
    .min(2, 'Imię musi mieć co najmniej 2 znaki')
    .max(50, 'Imię nie może być dłuższe niż 50 znaków'),
  
  email: z.string()
    .email('Nieprawidłowy adres email'),
  
  phone: z.string()
    .regex(/^[0-9+\s-()]{9,15}$/, 'Nieprawidłowy numer telefonu'),
  
  eventId: z.string()
    .uuid('Nieprawidłowy identyfikator wydarzenia'),
  
  participants: z.number()
    .min(1, 'Musisz wybrać co najmniej jednego uczestnika')
    .max(10, 'Maksymalnie 10 uczestników'),
  
  specialRequests: z.string()
    .max(500, 'Uwagi nie mogą być dłuższe niż 500 znaków')
    .optional(),
}); 