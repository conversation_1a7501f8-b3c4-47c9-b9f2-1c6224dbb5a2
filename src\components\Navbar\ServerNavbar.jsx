import React from 'react';

// Static navigation links to avoid import issues
const staticNavLinks = [
  { href: '/', label: 'Strona główna' },
  { href: '/blog', label: 'Blog' },
  {
    href: '/program',
    label: 'Program',
    submenu: [
      { href: '/program?destination=bali', label: 'Bali - 12 dni' },
      { href: '/program?destination=srilanka', label: 'Sri Lanka - 10 dni' }
    ]
  },
  { href: '/zajecia-online', label: 'Zajęcia Online' },
  { href: '/o-mnie', label: 'O mnie' },
  { href: '/galeria', label: 'Galeria' },
  { href: '/kontakt', label: 'Kontakt' },
];

export default function ServerNavbar() {
  return (
    <>
      <header className="fixed top-0 w-full z-50 bg-secondary/98 backdrop-blur-md border-b border-primary/5 transition-all duration-300 h-20 md:h-20">
        <div className="container mx-auto px-6 h-full">
          <div className="flex items-center justify-between h-full">
            {/* Logo - BAKASANA w Playfair Display */}
            <a href="/" className="flex items-center group">
              <span className="text-2xl font-serif font-light text-accent tracking-wider transition-all duration-300 group-hover:text-primary">
                BAKASANA
              </span>
            </a>

            {/* Nawigacja desktopowa - elegancka i minimalistyczna */}
            <nav className="hidden md:flex items-center space-x-10">
              {staticNavLinks.map((link) => (
                <div key={link.href} className="relative group">
                  <a
                    href={link.href}
                    className="text-sm font-light text-primary/70 hover:opacity-70 transition-opacity duration-300 px-4 py-2 tracking-wide relative group"
                  >
                    {link.label}
                    {link.submenu && (
                      <span className="ml-2 text-xs opacity-60">▼</span>
                    )}
                  </a>

                  {/* Minimalistyczne dropdown menu */}
                  {link.submenu && (
                    <div className="absolute top-full left-0 mt-3 bg-secondary/98 backdrop-blur-lg shadow-soft rounded-xl border border-primary/10 p-6 min-w-[240px] opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300">
                      {link.submenu.map((sublink, index) => (
                        <a
                          key={sublink.href}
                          href={sublink.href}
                          className="block text-sm font-light text-primary/70 hover:opacity-70 transition-opacity duration-300 py-4 px-4 rounded-lg tracking-wide"
                        >
                          {sublink.label}
                        </a>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </nav>

            {/* Mobilne menu - ultra minimalistyczne */}
            <div className="md:hidden">
              <details className="relative group">
                <summary className="text-accent cursor-pointer font-light text-sm tracking-wide transition-opacity duration-300 hover:opacity-70 list-none">
                  <span className="flex items-center gap-2">
                    Menu
                    <div className="w-5 h-px bg-accent"></div>
                  </span>
                </summary>
                <nav className="absolute right-0 top-full mt-3 bg-secondary/98 backdrop-blur-lg shadow-soft rounded-xl border border-primary/10 p-6 min-w-[220px]">
                  {staticNavLinks.map((link, index) => (
                    <a
                      key={link.href}
                      href={link.href}
                      className="block text-sm font-light text-primary/70 hover:opacity-70 transition-opacity duration-300 py-3 px-3 rounded-lg tracking-wide"
                    >
                      {link.label}
                    </a>
                  ))}
                  <div className="mt-4 pt-4 border-t border-primary/10">
                    <a
                      href="tel:+48606101523"
                      className="block text-sm font-light text-accent py-2 hover:opacity-70 transition-opacity duration-300"
                    >
                      +48 606 101 523
                    </a>
                  </div>
                </nav>
              </details>
            </div>
          </div>
        </div>
      </header>
    </>
  );
}