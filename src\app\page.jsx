import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import LazyImage from '@/components/ui/LazyImage';

// Fallback mock data for testing
const mockBlogPosts = [
  {
    slug: 'stanie-na-rekach-odkryj-swoja-wewnetrzna-sile-i-odwage',
    title: 'Stanie na Rękach: Odkryj Swoją Wewnętrzną Siłę i Odwagę',
    excerpt: 'Przek<PERSON>cz granice strachu i odkryj niesamowitą moc swojego ciała.',
    imageUrl: '/images/blog/handstand-828.webp',
  },
  {
    slug: 'szpagaty-otworz-biodra-i-uwolnij-swoja-kobiecosc',
    title: 'Szpagaty: Otwórz Biodra i Uwolnij Swoją Kobiecość',
    excerpt: 'Odkryj sekret elastyczności i gracji.',
    imageUrl: '/images/blog/temple-bali.webp',
  },
  {
    slug: 'kobieca-sila-w-jodze-odkryj-swoja-wewnetrzna-boginie',
    title: 'Kobieca Siła w Jodze: Odkryj Swoją Wewnętrzną Boginię',
    excerpt: 'Poznaj unikalne aspekty praktyki jogi dla kobiet.',
    imageUrl: '/images/blog/temple-bali.webp',
  }
];

// Simple icon placeholders to avoid import issues
const IconPlaceholder = ({ className = 'w-5 h-5' }) => (
  <div className={`${className} bg-temple/20 rounded`} />
);

// SSR-friendly SafeIcon component - using placeholder for now
const SafeIcon = ({ className = 'w-5 h-5' }) => {
  return <IconPlaceholder className={className} />;
};

const HeroSection = () => {
  return (
    <section className="relative w-full min-h-screen flex items-center justify-center overflow-hidden bg-secondary">
      {/* Tło z delikatnym overlay */}
      <div className="absolute inset-0 z-0">
        <div
          className="w-full h-full bg-cover bg-center"
          style={{
            backgroundImage: `linear-gradient(rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.6)), url('/images/background/bali-hero-low-res.webp')`
          }}
        />
      </div>

      {/* Treść wycentrowana - ultra minimalistyczna */}
      <div className="relative z-10 text-center max-w-4xl mx-auto px-8">
        <h1 className="text-5xl md:text-6xl lg:text-7xl font-serif font-light mb-12 tracking-tight text-primary leading-tight">
          Odkryj Wewnętrzną<br />Harmonię
        </h1>

        <p className="text-xl md:text-2xl mb-16 text-primary/80 font-light max-w-2xl mx-auto leading-relaxed">
          Transformacyjne retreaty jogowe na Bali i Sri Lance
        </p>

        {/* Ultra minimalistyczny button */}
        <div>
          <Link
            href="#o-mnie"
            className="inline-block px-16 py-5 border border-primary text-primary font-light uppercase tracking-wider text-sm hover:bg-primary hover:text-secondary transition-all duration-300"
            aria-label="Poznaj więcej o retreatach"
          >
            Poznaj więcej
          </Link>
        </div>
      </div>
    </section>
  );
};

const Card = ({ type = 'post', title, description, link, imageUrl, index = 0 }) => {
  const isHighlight = type === 'highlight';
  const isTestimonial = type === 'testimonial';

  if (isHighlight) {
    return (
      <div className="group bg-secondary shadow-soft hover:shadow-medium transition-shadow duration-300 rounded-xl min-h-[280px]">
        <div className="p-10 flex flex-col flex-grow h-full">
          <h3 className="text-2xl font-serif font-light mb-8 tracking-wide text-primary">{title}</h3>
          <p className="font-light text-lg leading-relaxed flex-grow text-text-light">{description}</p>
        </div>
      </div>
    );
  }

  if (isTestimonial) {
    return (
      <div className="group bg-background/50 shadow-soft hover:shadow-medium transition-shadow duration-300 rounded-xl min-h-[260px]">
        <div className="p-10 flex flex-col flex-grow h-full relative">
          <blockquote className="relative">
            <p className="leading-relaxed mb-8 italic text-lg text-text-light">"{description}"</p>
            <footer className="flex items-center mt-auto">
              <div className="w-12 h-12 rounded-full bg-accent/10 flex items-center justify-center font-serif text-lg">
                {title.charAt(0)}
              </div>
              <div className="ml-4">
                <cite className="block font-medium text-base not-italic text-primary">{title}</cite>
                <p className="text-sm text-text-light uppercase tracking-wider">{link}</p>
              </div>
            </footer>
          </blockquote>
        </div>
      </div>
    );
  }

  return (
    <div className="group h-full">
      <div className="bg-secondary shadow-soft hover:shadow-medium transition-shadow duration-300 h-full flex flex-col rounded-xl overflow-hidden">
        <div className="relative h-[300px] overflow-hidden">
          <LazyImage
            src={imageUrl || '/images/placeholder/image.jpg'}
            alt={title}
            fill
            className="object-cover"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
        </div>
        <div className="p-10 flex flex-col flex-grow">
          <h3 className="text-2xl font-serif mb-6 font-light tracking-wide">
            <Link href={link || '#'} className="hover:opacity-70 transition-opacity duration-300 text-primary">
              {title}
            </Link>
          </h3>
          <p className="text-lg leading-relaxed mb-8 flex-grow font-light text-text-light">{description}</p>
          <Link
            href={link || '#'}
            className="inline-flex items-center gap-2 text-sm font-light mt-auto self-start uppercase tracking-wider text-accent hover:opacity-70 transition-opacity duration-300"
          >
            <span>Czytaj dalej</span>
            <span>→</span>
          </Link>
        </div>
      </div>
    </div>
  );
};

// Static data - no need for useMemo in Server Component
const homepagePosts = mockBlogPosts;

const eventData = {
  highlights: [
    {
      id: 'ubud',
      title: 'Ubud, Bali',
      description: 'Duchowe serce Bali, zanurzenie w kulturze i jodze wśród tarasów ryżowych i świętych świątyń.',
      destination: 'bali'
    },
    {
      id: 'sigiriya',
      title: 'Sigiriya, Sri Lanka',
      description: 'Lwia Skała - starożytna forteca na wysokości 200m, idealna na medytację i praktykę mindfulness.',
      destination: 'srilanka'
    },
    {
      id: 'gili-air',
      title: 'Gili Air, Bali',
      description: 'Rajska wyspa bez samochodów, idealna na relaks, snorkeling i medytację nad oceanem.',
      destination: 'bali'
    },
    {
      id: 'ella',
      title: 'Ella, Sri Lanka',
      description: 'Górski raj z plantacjami herbaty, idealny na retreaty jogowe z widokiem na nieskończone wzgórza.',
      destination: 'srilanka'
    },
    {
      id: 'uluwatu',
      title: 'Uluwatu, Bali',
      description: 'Joga na klifach z widokiem na bezkresny ocean i spektakularne zachody słońca.',
      destination: 'bali'
    },
  ],
};

const combinedItems = eventData.highlights.map((item) => ({ ...item, type: 'highlight' }));

const socialLinks = [
  {
    id: 'instagram',
    href: 'https://www.instagram.com/fly_with_bakasana?igsh=MWpmanNpeHVodTlubA%3D%3D&utm_source=qr',
    label: 'Instagram',
    aria: 'Profil na Instagramie',
  },
  {
    id: 'facebook',
    href: 'https://www.facebook.com/p/Fly-with-bakasana-100077568306563/',
    label: 'Facebook',
    aria: 'Profil na Facebooku',
  },
  {
    id: 'fitssey',
    href: 'https://app.fitssey.com/Flywithbakasana/frontoffice',
    label: 'Fitssey',
    aria: 'Profil na Fitssey (rezerwacje)',
  },
];

export default function HomePage() {

  return (
    <div className="relative">
      {/* 1. HERO - pełny ekran z jednym zdaniem i przyciskiem */}
      <HeroSection />

      {/* 2. O MNIE - ultra minimalistyczna sekcja */}
      <section id="o-mnie" className="section-padding bg-secondary relative">
        <div className="container-unified">
          <div className="grid lg:grid-cols-2 gap-20 lg:gap-32 items-center">
            {/* Zdjęcie po lewej */}
            <div className="relative">
              <div className="aspect-[4/5] relative overflow-hidden rounded-lg">
                <Image
                  src="/images/profile/omnie-opt.webp"
                  alt="Julia Jakubowicz - instruktorka jogi"
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, 50vw"
                />
              </div>
            </div>

            {/* Tekst po prawej */}
            <div className="space-y-12">
              <div>
                <h2 className="text-4xl md:text-5xl font-serif font-light mb-8 text-accent">
                  Cześć, jestem Julia
                </h2>
                <div className="w-24 h-px bg-accent mb-12"></div>
              </div>

              <div className="space-y-8 text-lg leading-relaxed">
                <p className="text-text-light">
                  Jestem fizjoterapeutką i instruktorką jogi z ponad 8-letnim doświadczeniem.
                  Moją pasją jest łączenie tradycyjnej praktyki jogi z nowoczesną wiedzą o ciele.
                </p>

                <p className="text-text-light">
                  Od 5 lat organizuję retreaty na Bali i Sri Lance, tworząc przestrzeń dla
                  transformacji i głębokiego połączenia z sobą. Każdy wyjazd to starannie
                  zaplanowana podróż łącząca praktykę jogi, medytację i odkrywanie kultury.
                </p>

                <p className="text-text-light">
                  Wierzę, że joga to nie tylko ćwiczenia - to styl życia, który przynosi
                  harmonię ciała, umysłu i ducha. Zapraszam Cię do odkrycia tej magii razem ze mną.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
      {/* 3. OFERTA - ultra czyste karty */}
      <section className="section-padding bg-background relative">
        <div className="container-unified">
          <div className="section-unified-title">
            <h2 className="text-4xl md:text-5xl font-serif font-light text-accent">Nasze Retreaty</h2>
            <p className="text-xl font-light text-text-light">Wybierz swoją transformacyjną podróż</p>
          </div>

          <div className="grid md:grid-cols-3 gap-12">
            {/* Karta 1 - Bali */}
            <div className="group bg-secondary rounded-xl shadow-soft hover:shadow-medium transition-shadow duration-300 overflow-hidden">
              <div className="aspect-[4/3] relative overflow-hidden">
                <Image
                  src="/images/programs/program1.webp"
                  alt="Retreat jogi na Bali"
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, 33vw"
                />
              </div>
              <div className="p-12">
                <h3 className="text-2xl font-serif font-light mb-6 text-primary">Bali Retreat</h3>
                <p className="text-text-light leading-relaxed mb-8">
                  7-dniowa podróż przez duchowe serce Bali. Joga, medytacja,
                  tarasy ryżowe i święte świątynie Ubud.
                </p>
                <div className="text-accent font-light text-sm uppercase tracking-wider">
                  Od 3500 PLN
                </div>
              </div>
            </div>

            {/* Karta 2 - Sri Lanka */}
            <div className="group bg-secondary rounded-xl shadow-soft hover:shadow-medium transition-shadow duration-300 overflow-hidden">
              <div className="aspect-[4/3] relative overflow-hidden">
                <Image
                  src="/images/programs/program2.webp"
                  alt="Retreat jogi na Sri Lance"
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, 33vw"
                />
              </div>
              <div className="p-12">
                <h3 className="text-2xl font-serif font-light mb-6 text-primary">Sri Lanka Retreat</h3>
                <p className="text-text-light leading-relaxed mb-8">
                  10-dniowa przygoda łącząca jogę z ayurvedą. Sigiriya,
                  Ella i plantacje herbaty.
                </p>
                <div className="text-accent font-light text-sm uppercase tracking-wider">
                  Od 4200 PLN
                </div>
              </div>
            </div>

            {/* Karta 3 - Zajęcia Online */}
            <div className="group bg-secondary rounded-xl shadow-soft hover:shadow-medium transition-shadow duration-300 overflow-hidden">
              <div className="aspect-[4/3] relative overflow-hidden">
                <Image
                  src="/images/programs/program3.webp"
                  alt="Zajęcia jogi online"
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, 33vw"
                />
              </div>
              <div className="p-12">
                <h3 className="text-2xl font-serif font-light mb-6 text-primary">Zajęcia Online</h3>
                <p className="text-text-light leading-relaxed mb-8">
                  Regularne zajęcia jogi online. Praktykuj z domu
                  w małych grupach z indywidualnym podejściem.
                </p>
                <div className="text-accent font-light text-sm uppercase tracking-wider">
                  Od 80 PLN/zajęcia
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 4. TESTIMONIALE - ultra eleganckie cytaty */}
      <section className="section-padding bg-secondary relative">
        <div className="container-unified">
          <div className="section-unified-title">
            <h2 className="text-4xl md:text-5xl font-serif font-light text-accent">Co mówią uczestnicy</h2>
            <p className="text-xl font-light text-text-light">Poznaj doświadczenia osób, które odkryły harmonię z nami</p>
          </div>

          {/* Główny testimonial - większy */}
          <div className="max-w-4xl mx-auto mb-20">
            <div className="bg-background/30 rounded-xl p-16 text-center relative">
              <blockquote className="text-2xl md:text-3xl font-serif font-light italic text-primary mb-12 leading-relaxed">
                "Retreat z Julią to najlepsze, co mogłam dla siebie zrobić. Połączenie jogi,
                medytacji i eksploracji Bali było idealnie wyważone. Wróciłam odmieniona!"
              </blockquote>
              <footer className="text-accent font-light">
                <cite className="not-italic text-lg">— Marta K., Warszawa</cite>
              </footer>
            </div>
          </div>

          {/* Mniejsze testimoniale w rzędzie */}
          <div className="grid md:grid-cols-2 gap-12 max-w-5xl mx-auto">
            <div className="bg-background/20 rounded-xl p-10">
              <blockquote className="text-lg font-light italic text-primary mb-8 leading-relaxed">
                "Jako początkujący w jodze obawiałem się, czy dam radę. Julia stworzyła przestrzeń,
                w której każdy mógł praktykować na swoim poziomie."
              </blockquote>
              <footer className="text-accent font-light">
                <cite className="not-italic">— Tomasz W., Kraków</cite>
              </footer>
            </div>

            <div className="bg-background/20 rounded-xl p-10">
              <blockquote className="text-lg font-light italic text-primary mb-8 leading-relaxed">
                "Trzeci raz uczestniczę w retreatach Julii i za każdym razem odkrywam coś nowego -
                zarówno w praktyce jogi, jak i w sobie."
              </blockquote>
              <footer className="text-accent font-light">
                <cite className="not-italic">— Karolina M., Wrocław</cite>
              </footer>
            </div>
          </div>
        </div>
      </section>
      {/* 5. CTA - ultra minimalistyczna sekcja */}
      <section className="relative section-padding overflow-hidden">
        {/* Tło zdjęciowe z delikatnym overlay */}
        <div className="absolute inset-0 z-0">
          <div
            className="w-full h-full bg-cover bg-center"
            style={{
              backgroundImage: `linear-gradient(rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.9)), url('/images/programs/tarasy-ryzowe.avif')`
            }}
          />
        </div>

        <div className="container-unified relative z-10">
          <div className="text-center max-w-4xl mx-auto">
            <h2 className="text-5xl md:text-6xl font-serif font-light mb-12 text-primary">
              Gotowa na transformację?
            </h2>

            <div className="w-32 h-px bg-accent mx-auto mb-16"></div>

            <p className="text-xl md:text-2xl font-light text-text-light mb-16 leading-relaxed">
              Dołącz do naszego najbliższego retreatu i odkryj harmonię ciała, umysłu i ducha
              w magicznych miejscach Bali lub Sri Lanki.
            </p>

            <div className="flex flex-col sm:flex-row gap-8 justify-center">
              <Link
                href="/kontakt"
                className="inline-block px-16 py-5 bg-accent text-secondary font-light uppercase tracking-wider text-sm hover:opacity-90 transition-opacity duration-300"
                aria-label="Skontaktuj się z nami"
              >
                Skontaktuj się z nami
              </Link>

              <Link
                href="/program"
                className="inline-block px-16 py-5 border border-primary text-primary font-light uppercase tracking-wider text-sm hover:bg-primary hover:text-secondary transition-all duration-300"
                aria-label="Zobacz program retreatów"
              >
                Zobacz program
              </Link>
            </div>
          </div>
        </div>
      </section>
      {/* 6. FOOTER - minimalistyczny (będzie w komponencie Footer) */}



      {/* WhatsApp Button - ultra minimalistyczny */}
      <a
        href="https://wa.me/48606101523?text=Cześć! Interesuję się retreatami jogowymi BAKASANA na Bali lub Sri Lance. Czy możesz mi przesłać więcej informacji?"
        target="_blank"
        rel="noopener noreferrer"
        className="fixed bottom-8 right-8 bg-accent hover:opacity-90 text-secondary p-4 rounded-full shadow-soft transition-opacity duration-300 z-50 group"
        aria-label="Skontaktuj się przez WhatsApp"
      >
        <svg
          className="w-6 h-6"
          fill="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
        </svg>

        {/* Tooltip - minimalistyczny */}
        <div className="absolute bottom-full right-0 mb-3 px-3 py-2 bg-primary text-secondary text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap font-light">
          Napisz na WhatsApp
        </div>
      </a>
    </div>
  );
}
