import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import LazyImage from '@/components/ui/LazyImage';
import ScrollFadeIn from '@/components/ScrollFadeIn';

// Fallback mock data for testing
const mockBlogPosts = [
  {
    slug: 'stanie-na-rekach-odkryj-swoja-wewnetrzna-sile-i-odwage',
    title: '<PERSON>ie na Rękach: Odkryj Swoją Wewnętrzną Siłę i Odwagę',
    excerpt: 'Przekrocz granice strachu i odkryj niesamowitą moc swojego ciała.',
    imageUrl: '/images/blog/handstand-828.webp',
  },
  {
    slug: 'szpagaty-otworz-biodra-i-uwolnij-swoja-kobiecosc',
    title: 'Szpagaty: Otwórz Biodra i Uwolnij Swoją Kobiecość',
    excerpt: 'Odkryj sekret elastyczności i gracji.',
    imageUrl: '/images/blog/temple-bali.webp',
  },
  {
    slug: 'kobieca-sila-w-jodze-odkryj-swoja-wewnetrzna-boginie',
    title: 'Kobieca Siła w Jodze: Odkryj Swoją Wewnętrzną Boginię',
    excerpt: 'Poznaj unikalne aspekty praktyki jogi dla kobiet.',
    imageUrl: '/images/blog/temple-bali.webp',
  }
];

// Simple icon placeholders to avoid import issues
const IconPlaceholder = ({ className = 'w-5 h-5' }) => (
  <div className={`${className} bg-temple/20 rounded`} />
);

// SSR-friendly SafeIcon component - using placeholder for now
const SafeIcon = ({ className = 'w-5 h-5' }) => {
  return <IconPlaceholder className={className} />;
};

const HeroSection = () => {
  return (
    <section className="relative w-full h-screen flex items-center justify-center overflow-hidden">
      {/* Tło z ultra delikatnym overlay */}
      <div className="absolute inset-0 z-0">
        <div
          className="w-full h-full bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: `linear-gradient(rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.6)), url('/images/background/bali-hero-low-res.webp')`
          }}
        />
      </div>

      {/* Treść wycentrowana - ultra minimalistyczna */}
      <div className="relative z-10 text-center max-w-4xl mx-auto px-8">
        <h1 className="text-6xl md:text-7xl lg:text-8xl font-serif font-extralight tracking-tight text-primary mb-8 leading-tight">
          Odkryj Wewnętrzną<br />
          Harmonię
        </h1>

        <p className="text-xl md:text-2xl font-light text-primary/80 mb-16 max-w-2xl mx-auto leading-relaxed">
          Transformacyjne retreaty jogowe na Bali i Sri Lance
        </p>

        {/* Prostokątny button bez zaokrągleń */}
        <div>
          <Link
            href="#journey"
            className="inline-block px-20 py-5 border border-primary/30 text-primary hover:bg-primary hover:text-secondary transition-all duration-500 font-light uppercase tracking-wider text-sm"
            aria-label="Rozpocznij podróż"
            style={{ borderRadius: 0 }}
          >
            Rozpocznij podróż
          </Link>
        </div>
      </div>

      {/* Delikatny scroll indicator */}
      <div className="absolute bottom-12 left-1/2 transform -translate-x-1/2">
        <div className="w-px h-16 bg-primary/20 relative">
          <div className="absolute top-0 w-px h-8 bg-primary/40 animate-scroll-down"></div>
        </div>
      </div>
    </section>
  );
};

const Card = ({ type = 'post', title, description, link, imageUrl, index = 0 }) => {
  const isHighlight = type === 'highlight';
  const isTestimonial = type === 'testimonial';

  if (isHighlight) {
    return (
      <div className="group bg-secondary shadow-soft hover:shadow-medium transition-shadow duration-300 rounded-xl min-h-[280px]">
        <div className="p-10 flex flex-col flex-grow h-full">
          <h3 className="text-2xl font-serif font-light mb-8 tracking-wide text-primary">{title}</h3>
          <p className="font-light text-lg leading-relaxed flex-grow text-text-light">{description}</p>
        </div>
      </div>
    );
  }

  if (isTestimonial) {
    return (
      <div className="group bg-background/50 shadow-soft hover:shadow-medium transition-shadow duration-300 rounded-xl min-h-[260px]">
        <div className="p-10 flex flex-col flex-grow h-full relative">
          <blockquote className="relative">
            <p className="leading-relaxed mb-8 italic text-lg text-text-light">"{description}"</p>
            <footer className="flex items-center mt-auto">
              <div className="w-12 h-12 rounded-full bg-accent/10 flex items-center justify-center font-serif text-lg">
                {title.charAt(0)}
              </div>
              <div className="ml-4">
                <cite className="block font-medium text-base not-italic text-primary">{title}</cite>
                <p className="text-sm text-text-light uppercase tracking-wider">{link}</p>
              </div>
            </footer>
          </blockquote>
        </div>
      </div>
    );
  }

  return (
    <div className="group h-full">
      <div className="bg-secondary shadow-soft hover:shadow-medium transition-shadow duration-300 h-full flex flex-col rounded-xl overflow-hidden">
        <div className="relative h-[300px] overflow-hidden">
          <LazyImage
            src={imageUrl || '/images/placeholder/image.jpg'}
            alt={title}
            fill
            className="object-cover"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
        </div>
        <div className="p-10 flex flex-col flex-grow">
          <h3 className="text-2xl font-serif mb-6 font-light tracking-wide">
            <Link href={link || '#'} className="hover:opacity-70 transition-opacity duration-300 text-primary">
              {title}
            </Link>
          </h3>
          <p className="text-lg leading-relaxed mb-8 flex-grow font-light text-text-light">{description}</p>
          <Link
            href={link || '#'}
            className="inline-flex items-center gap-2 text-sm font-light mt-auto self-start uppercase tracking-wider text-accent hover:opacity-70 transition-opacity duration-300"
          >
            <span>Czytaj dalej</span>
            <span>→</span>
          </Link>
        </div>
      </div>
    </div>
  );
};

// Static data - no need for useMemo in Server Component
const homepagePosts = mockBlogPosts;

const eventData = {
  highlights: [
    {
      id: 'ubud',
      title: 'Ubud, Bali',
      description: 'Duchowe serce Bali, zanurzenie w kulturze i jodze wśród tarasów ryżowych i świętych świątyń.',
      destination: 'bali'
    },
    {
      id: 'sigiriya',
      title: 'Sigiriya, Sri Lanka',
      description: 'Lwia Skała - starożytna forteca na wysokości 200m, idealna na medytację i praktykę mindfulness.',
      destination: 'srilanka'
    },
    {
      id: 'gili-air',
      title: 'Gili Air, Bali',
      description: 'Rajska wyspa bez samochodów, idealna na relaks, snorkeling i medytację nad oceanem.',
      destination: 'bali'
    },
    {
      id: 'ella',
      title: 'Ella, Sri Lanka',
      description: 'Górski raj z plantacjami herbaty, idealny na retreaty jogowe z widokiem na nieskończone wzgórza.',
      destination: 'srilanka'
    },
    {
      id: 'uluwatu',
      title: 'Uluwatu, Bali',
      description: 'Joga na klifach z widokiem na bezkresny ocean i spektakularne zachody słońca.',
      destination: 'bali'
    },
  ],
};

const combinedItems = eventData.highlights.map((item) => ({ ...item, type: 'highlight' }));

const socialLinks = [
  {
    id: 'instagram',
    href: 'https://www.instagram.com/fly_with_bakasana?igsh=MWpmanNpeHVodTlubA%3D%3D&utm_source=qr',
    label: 'Instagram',
    aria: 'Profil na Instagramie',
  },
  {
    id: 'facebook',
    href: 'https://www.facebook.com/p/Fly-with-bakasana-100077568306563/',
    label: 'Facebook',
    aria: 'Profil na Facebooku',
  },
  {
    id: 'fitssey',
    href: 'https://app.fitssey.com/Flywithbakasana/frontoffice',
    label: 'Fitssey',
    aria: 'Profil na Fitssey (rezerwacje)',
  },
];

export default function HomePage() {

  return (
    <div className="relative">
      {/* 1. HERO - pełny ekran z jednym zdaniem i przyciskiem */}
      <HeroSection />

      {/* 2. CYTAT SECTION - MINIMALISTYCZNY SEPARATOR */}
      <ScrollFadeIn>
        <section className="py-32 bg-white relative">
          <div className="max-w-3xl mx-auto px-8 text-center">
            <p className="text-2xl md:text-3xl font-serif font-extralight italic text-primary/70 leading-relaxed">
              "Joga to nie tylko praktyka fizyczna,<br />
              to podróż w głąb siebie"
            </p>
            <div className="w-24 h-px bg-accent/30 mx-auto mt-12"></div>
          </div>
        </section>
      </ScrollFadeIn>

      {/* 3. O MNIE - BEZ KART, PŁYNNE PRZEJŚCIE */}
      <ScrollFadeIn delay={200}>
        <section id="journey" className="py-32 bg-gradient-to-b from-white to-background/30">
          <div className="max-w-7xl mx-auto px-8">
            <div className="grid lg:grid-cols-2 gap-20 items-center">
              {/* Zdjęcie bez zaokrągleń */}
              <div className="relative">
                <Image
                  src="/images/profile/omnie-opt.webp"
                  alt="Julia Jakubowicz - instruktorka jogi"
                  width={600}
                  height={750}
                  className="w-full shadow-2xl"
                  style={{ borderRadius: 0 }}
                  sizes="(max-width: 768px) 100vw, 50vw"
                />
                {/* Delikatny akcent */}
                <div className="absolute -bottom-4 -right-4 w-32 h-32 border border-accent/20 -z-10"></div>
              </div>

              <div className="space-y-8">
                <h2 className="text-5xl font-serif font-extralight text-primary">
                  Cześć, jestem Julia
                </h2>
                <div className="w-16 h-px bg-accent/40"></div>

                <div className="space-y-6 text-lg leading-relaxed text-primary/80 font-light">
                  <p>
                    Jestem magistrem fizjoterapii z 8-letnim doświadczeniem klinicznym oraz
                    certyfikowaną instruktorką jogi (RYT 500).
                  </p>
                  <p>
                    Organizuję retreaty jogowe na Bali, gdzie dzielę się nie tylko technikami jogi,
                    ale także magią tej wyjątkowej wyspy.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>
      </ScrollFadeIn>
      {/* 4. OFERTA - KARTY ZLANE Z TŁEM */}
      <ScrollFadeIn delay={400}>
        <section className="py-32 bg-background/30">
          <div className="max-w-7xl mx-auto px-8">
            <div className="text-center mb-20">
              <h2 className="text-5xl font-serif font-extralight text-primary mb-6">
                Nasze Retreaty
              </h2>
              <p className="text-xl text-primary/60 font-light">
                Wybierz swoją transformacyjną podróż
              </p>
            </div>

          <div className="grid lg:grid-cols-3 gap-12">
            {/* Karta - bez cieni, minimalne borders */}
            <div className="group cursor-pointer">
              <div className="relative overflow-hidden mb-8">
                <Image
                  src="/images/programs/program1.webp"
                  alt="Bali Retreat"
                  width={400}
                  height={300}
                  className="w-full aspect-[4/3] object-cover group-hover:scale-105 transition-transform duration-700"
                  style={{ borderRadius: 0 }}
                  sizes="(max-width: 768px) 100vw, 33vw"
                />
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-500"></div>
              </div>
              <h3 className="text-2xl font-serif font-light mb-4 group-hover:text-accent transition-colors">
                Bali Retreat
              </h3>
              <p className="text-primary/70 font-light mb-6">
                7-dniowa podróż przez duchowe serce Bali
              </p>
              <div className="flex items-center text-accent font-light">
                <span>Odkryj więcej</span>
                <span className="ml-2 group-hover:translate-x-2 transition-transform">→</span>
              </div>
            </div>

            {/* Karta 2 */}
            <div className="group cursor-pointer">
              <div className="relative overflow-hidden mb-8">
                <Image
                  src="/images/programs/program2.webp"
                  alt="Sri Lanka Retreat"
                  width={400}
                  height={300}
                  className="w-full aspect-[4/3] object-cover group-hover:scale-105 transition-transform duration-700"
                  style={{ borderRadius: 0 }}
                  sizes="(max-width: 768px) 100vw, 33vw"
                />
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-500"></div>
              </div>
              <h3 className="text-2xl font-serif font-light mb-4 group-hover:text-accent transition-colors">
                Sri Lanka Retreat
              </h3>
              <p className="text-primary/70 font-light mb-6">
                10-dniowa przygoda łącząca jogę z ayurvedą
              </p>
              <div className="flex items-center text-accent font-light">
                <span>Odkryj więcej</span>
                <span className="ml-2 group-hover:translate-x-2 transition-transform">→</span>
              </div>
            </div>

            {/* Karta 3 */}
            <div className="group cursor-pointer">
              <div className="relative overflow-hidden mb-8">
                <Image
                  src="/images/programs/program3.webp"
                  alt="Zajęcia Online"
                  width={400}
                  height={300}
                  className="w-full aspect-[4/3] object-cover group-hover:scale-105 transition-transform duration-700"
                  style={{ borderRadius: 0 }}
                  sizes="(max-width: 768px) 100vw, 33vw"
                />
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-500"></div>
              </div>
              <h3 className="text-2xl font-serif font-light mb-4 group-hover:text-accent transition-colors">
                Zajęcia Online
              </h3>
              <p className="text-primary/70 font-light mb-6">
                Praktykuj z domu w małych grupach
              </p>
              <div className="flex items-center text-accent font-light">
                <span>Odkryj więcej</span>
                <span className="ml-2 group-hover:translate-x-2 transition-transform">→</span>
              </div>
            </div>
          </div>
        </div>
      </section>
      </ScrollFadeIn>

      {/* 5. CYTAT PARALLAX - DELIKATNY EFEKT */}
      <ScrollFadeIn delay={600}>
        <section
          className="py-48 relative parallax-section"
          style={{ backgroundImage: "url('/images/programs/tarasy-ryzowe.avif')" }}
        >
          <div className="absolute inset-0 bg-white/85"></div>
          <div className="relative z-10 max-w-4xl mx-auto px-8 text-center">
            <p className="text-3xl md:text-4xl font-serif font-extralight italic text-primary leading-relaxed">
              "W ciszy odnajdujemy siebie,<br />
              w ruchu odkrywamy siłę"
            </p>
          </div>
        </section>
      </ScrollFadeIn>
      {/* 6. CTA - MINIMALISTYCZNY */}
      <ScrollFadeIn delay={800}>
        <section className="py-32 bg-white">
          <div className="text-center max-w-3xl mx-auto px-8">
            <h2 className="text-5xl font-serif font-extralight text-primary mb-8">
              Gotowa na transformację?
            </h2>
            <p className="text-xl text-primary/70 font-light mb-12">
              Dołącz do nas w podróży ku wewnętrznej harmonii
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Link
                href="/kontakt"
                className="px-12 py-4 bg-accent text-white hover:bg-accent/90 transition-colors font-light uppercase tracking-wider text-sm"
                style={{ borderRadius: 0 }}
              >
                Zarezerwuj miejsce
              </Link>
              <Link
                href="/program"
                className="px-12 py-4 border border-primary/30 text-primary hover:bg-primary hover:text-white transition-all font-light uppercase tracking-wider text-sm"
                style={{ borderRadius: 0 }}
              >
                Zobacz terminy
              </Link>
            </div>
          </div>
        </section>
      </ScrollFadeIn>
      {/* 6. FOOTER - minimalistyczny (będzie w komponencie Footer) */}



      {/* WhatsApp Button - prostokątny */}
      <a
        href="https://wa.me/48606101523?text=Cześć! Interesuję się retreatami jogowymi BAKASANA na Bali lub Sri Lance. Czy możesz mi przesłać więcej informacji?"
        target="_blank"
        rel="noopener noreferrer"
        className="fixed bottom-8 right-8 bg-accent hover:opacity-90 text-secondary p-4 shadow-soft transition-opacity duration-300 z-50 group"
        style={{ borderRadius: 0 }}
        aria-label="Skontaktuj się przez WhatsApp"
      >
        <svg
          className="w-6 h-6"
          fill="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
        </svg>

        {/* Tooltip - minimalistyczny */}
        <div className="absolute bottom-full right-0 mb-3 px-3 py-2 bg-primary text-secondary text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap font-light">
          Napisz na WhatsApp
        </div>
      </a>
    </div>
  );
}
