'use client';

import { useEffect } from 'react';

export function ClientAnalytics() {
  useEffect(() => {
    // Bezpieczne ładowanie analytics tylko w przeglądarce
    if (typeof window === 'undefined') return;

    // Google Analytics
    const GA_ID = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID;
    if (GA_ID && !window.gtag) {
      // Załaduj gtag script
      const script = document.createElement('script');
      script.async = true;
      script.src = `https://www.googletagmanager.com/gtag/js?id=${GA_ID}`;
      document.head.appendChild(script);

      // Inicjalizuj gtag
      window.dataLayer = window.dataLayer || [];
      function gtag(){window.dataLayer.push(arguments);}
      window.gtag = gtag;
      gtag('js', new Date());
      gtag('config', GA_ID);
    }

    // Web Vitals tracking (tylko w produkcji)
    if (process.env.NODE_ENV === 'production') {
      const trackWebVitals = async () => {
        try {
          const { onCLS, onFID, onLCP, onFCP, onTTFB } = await import('web-vitals');

          const sendMetric = ({ name, value, id }) => {
            if (window.gtag) {
              window.gtag('event', name, {
                value: Math.round(name === 'CLS' ? value * 1000 : value),
                event_category: 'Web Vitals',
                event_label: id,
                non_interaction: true,
              });
            }
          };

          onCLS(sendMetric);
          onFID(sendMetric);
          onLCP(sendMetric);
          onFCP(sendMetric);
          onTTFB(sendMetric);
        } catch (error) {
          console.warn('Web Vitals loading failed:', error);
        }
      };

      trackWebVitals();
    }
  }, []);

  return null;
}