'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Menu, X } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { navigationLinks } from '@/data/navigationLinks';

export default function Navbar() {
  const [isOpen, setIsOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const pathname = usePathname();
  const clickTimeoutRef = useRef(null);

  // Hydratacja fix
  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (typeof window === 'undefined') return;
    
    const handleScroll = () => {
      setScrolled(window.scrollY > 20);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Zamknij menu po zmianie strony
  useEffect(() => {
    setIsOpen(false);
  }, [pathname]);

  // Cleanup timeout przy unmount
  useEffect(() => {
    return () => {
      if (clickTimeoutRef.current) {
        clearTimeout(clickTimeoutRef.current);
      }
    };
  }, []);

  const navLinks = navigationLinks;

  // Poprawiona funkcja z debouncingiem
  const handleMenuToggle = () => {
    if (!isMounted || clickTimeoutRef.current) return;
    
    clickTimeoutRef.current = setTimeout(() => {
      setIsOpen(prev => !prev);
      clickTimeoutRef.current = null;
    }, 100);
  };

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        scrolled
          ? 'bg-secondary/98 backdrop-blur-md shadow-[0_2px_30px_rgba(0,0,0,0.05)] h-[60px]'
          : 'bg-secondary/95 backdrop-blur-sm shadow-[0_2px_30px_rgba(0,0,0,0.05)] h-[80px]'
      }`}
      suppressHydrationWarning
    >
      <div className="container mx-auto px-6 h-full">
        <div className="flex items-center justify-between h-full">
          {/* Logo BAKASANA - tylko napis, elegancki font */}
          <Link href="/" className="flex items-center">
            <span className="logo text-primary font-serif font-light tracking-tight">
              BAKASANA
            </span>
          </Link>

          {/* Nawigacja desktopowa - małe litery, cienka czcionka, rozstawione co 40px */}
          <nav className="hidden md:flex items-center space-x-10">
            {navLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                className="relative text-sm font-light lowercase tracking-wide text-primary/70 hover:text-primary transition-all duration-300 py-3 px-6 group"
                aria-current={pathname === link.href ? 'page' : undefined}
              >
                {link.label.toLowerCase()}
                {/* Animowane podkreślenie od dołu */}
                <span className="absolute bottom-0 left-6 right-6 h-px bg-primary transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></span>
              </Link>
            ))}
          </nav>

          {/* Przycisk mobilnego menu */}
          <button
            className="md:hidden text-primary"
            onClick={handleMenuToggle}
            aria-label={isOpen ? 'Zamknij menu' : 'Otwórz menu'}
            suppressHydrationWarning
          >
            {isMounted && isOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>
      </div>

      {/* Mobilne menu */}
      {isMounted && (
        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="md:hidden bg-secondary/98 backdrop-blur-md overflow-hidden border-t border-primary/10"
            >
              <nav className="container mx-auto px-6 py-6 flex flex-col space-y-4">
                {navLinks.map((link) => (
                  <Link
                    key={link.href}
                    href={link.href}
                    className="text-base py-3 block font-light lowercase tracking-wide text-primary/70 hover:text-primary transition-colors duration-300"
                    aria-current={pathname === link.href ? 'page' : undefined}
                    onClick={() => setIsOpen(false)}
                  >
                    {link.label.toLowerCase()}
                  </Link>
                ))}
              </nav>
            </motion.div>
          )}
        </AnimatePresence>
      )}
    </header>
  );
}