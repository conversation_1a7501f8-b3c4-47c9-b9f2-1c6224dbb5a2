/**
 * Funkcje pomocnicze do generowania metadanych dla App Router w Next.js
 * Zoptymalizowane dla retreatów jogowych na Bali
 */

/**
 * Generuje podstawowe metadane dla stron
 * @param {Object} options - Opcje metadanych
 * @returns {Object} - Obiekt metadanych zgodny z Next.js
 */
export function generateMetadata({
  title,
  description,
  keywords = [],
  image,
  url,
  type = 'website',
} = {}) {
  const siteName = 'Joga Bali 2025 | Retreat z Julią Jakubowicz | Od 2900 PLN';
  const siteDescription = '⭐ Sprawdzone retreaty jogi na Bali. ✓ Polska instruktorka ✓ Małe grupy ✓ All inclusive ✓ 97% zadowolonych. Zobacz opinie →';
  const siteImage = image || '/og-image.jpg';
  const siteUrl = url || process.env.NEXT_PUBLIC_BASE_URL || 'https://bakasana-travel.blog';

  const defaultKeywords = [
    // Główne słowa kluczowe 2025
    'joga Bali retreat 2025',
    'wyjazd joga Bali',
    'warsztaty jogi Ubud',
    'retreat jogi dla początkujących Bali',
    // Długi ogon - wysokiej konwersji
    'najlepszy retreat jogi na Bali opinie',
    'ile kosztuje wyjazd na jogę na Bali',
    'joga i medytacja Bali polska instruktorka',
    'bezpieczny wyjazd joga Bali dla kobiet',
    'retreat jogi Bali all inclusive',
    'Julia Jakubowicz instruktorka jogi',
    'fizjoterapeutka joga Bali',
    'małe grupy retreat jogi',
    'transformacyjny wyjazd Bali',
    'joga terapeutyczna Ubud',
    'Nusa Penida joga',
    'Gili Air retreat',
    'tarasy ryżowe medytacja',
    'świątynie Bali joga',
    'RYT 500 Yoga Alliance',
    'wellness Bali 2025'
  ];
  
  const siteKeywords = keywords.length > 0 ? [...keywords, ...defaultKeywords] : defaultKeywords;

  const fullTitle = title ? `${title} | ${siteName}` : siteName;

  return {
    title: fullTitle,
    description: description || siteDescription,
    keywords: siteKeywords,
    metadataBase: new URL('https://bakasana-travel.blog'),
    alternates: {
      canonical: url || '/',
    },
    openGraph: {
      title: fullTitle,
      description: description || siteDescription,
      url: 'https://bakasana-travel.blog' + (url || ''),
      siteName,
      images: [
        {
          url: siteImage,
          width: 1200,
          height: 630,
          alt: title || 'Joga na Bali - Retreaty Jogowe',
        },
      ],
      locale: 'pl_PL',
      type,
    },
    twitter: {
      card: 'summary_large_image',
      title: fullTitle,
      description: description || siteDescription,
      images: [siteImage],
      creator: '@julia_jakubowicz',
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-image-preview': 'large',
        'max-video-preview': -1,
        'max-snippet': -1,
      },
    },
    verification: {
      google: process.env.NEXT_PUBLIC_GOOGLE_VERIFICATION,
    },
    category: 'Health & Wellness',
    classification: 'Yoga Retreats',
    other: {
      'geo.region': 'ID-BA',
      'geo.placename': 'Bali, Indonesia',
      'geo.position': '-8.3405;115.0920',
      'ICBM': '-8.3405, 115.0920',
    },
  };
}

/**
 * Generuje metadane dla artykułów bloga
 * @param {Object} post - Dane artykułu
 * @returns {Object} - Obiekt metadanych zgodny z Next.js
 */
export function generateBlogMetadata(post) {
  if (!post) return generateMetadata();

  const blogKeywords = [
    'joga',
    'blog jogowy',
    'praktyka jogi',
    'mindfulness',
    'wellness',
    'zdrowie',
    'duchowość',
    ...(post.tags || [])
  ];

  return generateMetadata({
    title: post.title,
    description: post.excerpt || `${post.title} - Odkryj więcej na blogu o jodze i wellness.`,
    keywords: blogKeywords,
    image: post.imageUrl,
    url: `/blog/${post.slug}`,
    type: 'article',
  });
}

/**
 * Generuje strukturalne dane JSON-LD dla artykułów bloga
 * @param {Object} post - Dane artykułu
 * @returns {Object} - Obiekt JSON-LD dla BlogPosting
 */
export function generateBlogStructuredData(post) {
  if (!post) return null;

  return {
    '@context': 'https://schema.org',
    '@type': 'BlogPosting',
    headline: post.title,
    description: post.excerpt || post.description,
    image: {
      '@type': 'ImageObject',
      url: `https://bakasana-travel.blog${post.imageUrl}`,
      width: 1200,
      height: 630,
      alt: post.imageAlt || post.title
    },
    author: {
      '@type': 'Person',
      name: post.author || 'Julia Jakubowicz',
      jobTitle: 'Instruktorka Jogi i Fizjoterapeutka',
      url: 'https://bakasana-travel.blog/o-mnie'
    },
    publisher: {
      '@type': 'Organization',
      name: 'Joga na Bali - Julia Jakubowicz',
      logo: {
        '@type': 'ImageObject',
        url: 'https://bakasana-travel.blog/og-image.jpg'
      }
    },
    datePublished: post.date,
    dateModified: post.dateModified || post.date,
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': `https://bakasana-travel.blog/blog/${post.slug}`
    },
    keywords: post.tags ? post.tags.join(', ') : '',
    articleSection: post.category,
    wordCount: post.content ? post.content.replace(/<[^>]*>/g, '').split(' ').length : 500,
    timeRequired: `PT${Math.ceil((post.content ? post.content.replace(/<[^>]*>/g, '').split(' ').length : 500) / 200)}M`,
    inLanguage: 'pl-PL',
    isAccessibleForFree: true,
    about: [
      {
        '@type': 'Thing',
        name: 'Joga',
        sameAs: 'https://pl.wikipedia.org/wiki/Joga'
      },
      {
        '@type': 'Place',
        name: 'Bali',
        sameAs: 'https://pl.wikipedia.org/wiki/Bali'
      }
    ]
  };
}

/**
 * Generuje strukturalne dane JSON-LD dla SEO
 * @param {Object} options - Opcje danych strukturalnych
 * @returns {Object} - Obiekt JSON-LD
 */
export function generateStructuredData({
  type = 'Organization',
  name = 'Joga na Bali - Julia Jakubowicz',
  description = 'Profesjonalne retreaty jogowe na Bali z certyfikowaną instruktorką i fizjoterapeutką',
  url = 'https://bakasana-travel.blog',
  logo = '/og-image.jpg',
  image = '/og-image.jpg',
  telephone = '+48 606 101 523',
  email = '<EMAIL>',
  address = {
    addressCountry: 'PL',
    addressLocality: 'Rzeszów',
  },
  sameAs = [
    'https://www.instagram.com/fly_with_bakasana',
    'https://www.facebook.com/p/Fly-with-bakasana-100077568306563/',
    'https://flywithbakasana.pl/',
  ],
} = {}) {
  const baseStructure = {
    '@context': 'https://schema.org',
    '@type': type,
    name,
    description,
    url,
    logo: {
      '@type': 'ImageObject',
      url: logo,
    },
    image,
    telephone,
    email,
    address: {
      '@type': 'PostalAddress',
      ...address,
    },
    sameAs,
  };

  if (type === 'Person') {
    return {
      ...baseStructure,
      '@type': 'Person',
      jobTitle: 'Instruktorka Jogi i Fizjoterapeutka',
      knowsAbout: [
        'Joga',
        'Fizjoterapia',
        'Joga Terapeutyczna',
        'Retreaty Jogowe',
        'Mindfulness',
        'Wellness',
      ],
      hasCredential: [
        'Magister Fizjoterapii',
        'RYT 500 Yoga Alliance',
        'Certyfikowana Instruktorka Jogi',
      ],
    };
  }

  if (type === 'TravelAgency') {
    return {
      ...baseStructure,
      '@type': 'TravelAgency',
      serviceType: 'Yoga Retreats',
      areaServed: {
        '@type': 'Country',
        name: 'Indonesia',
        sameAs: 'https://en.wikipedia.org/wiki/Indonesia',
      },
      aggregateRating: {
        '@type': 'AggregateRating',
        ratingValue: '4.9',
        reviewCount: '47',
        bestRating: '5',
        worstRating: '1'
      },
      priceRange: '2900-4500 PLN',
      hasOfferCatalog: {
        '@type': 'OfferCatalog',
        name: 'Retreaty Jogowe na Bali',
        itemListElement: [
          {
            '@type': 'Offer',
            itemOffered: {
              '@type': 'Trip',
              name: '12-dniowy Retreat Jogowy na Bali',
              description: 'Transformacyjna podróż łącząca jogę z odkrywaniem Bali',
              provider: {
                '@type': 'Person',
                name: 'Julia Jakubowicz',
              },
            },
            price: '2900',
            priceCurrency: 'PLN',
            availability: 'https://schema.org/InStock',
            validFrom: '2025-01-01',
            validThrough: '2025-12-31'
          },
        ],
      },
    };
  }

  return baseStructure;
}